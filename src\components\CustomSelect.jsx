import Select from "react-select";

function CustomSelect({
  name,
  value,
  options,
  onChange,
  menuPlacement = "auto",
  showHoleTable = false,
}) {
  const handleChange = (selectedOption) => {
    onChange({
      target: {
        name,
        value: selectedOption ? selectedOption.value : "",
      },
    });
  };

  const selected = options.find((opt) => opt.value === value) || null;

  // Custom styles to control dropdown width and prevent popup stretching
  const customStyles = showHoleTable
    ? {
        menu: (provided) => ({
          ...provided,
          width: "300px", // Fixed width for the dropdown
          minWidth: "300px",
          maxWidth: "300px",
          zIndex: 9999,
          position: "fixed", // Use fixed positioning to break out of popup
        }),
        menuList: (provided) => ({
          ...provided,
          maxHeight: "210px",
        }),
        control: (provided) => ({
          ...provided,
          minHeight: "38px",
        }),
        menuPortal: (provided) => ({
          ...provided,
          zIndex: 9999,
        }),
      }
    : {};

  // Custom option component for hole table display
  const CustomOption = ({
    innerRef,
    innerProps,
    data,
    isSelected,
    isFocused,
  }) => (
    <div
      ref={innerRef}
      {...innerProps}
      className={`px-3 py-2 cursor-pointer border-b border-gray-200 ${
        isSelected
          ? "bg-blue-500 text-white"
          : isFocused
          ? "bg-gray-100"
          : "bg-white"
      }`}
    >
      {showHoleTable ? (
        <div className="grid grid-cols-2 gap-4 text-sm min-w-0">
          <div className="font-medium truncate">{data.hole_countersunk}</div>
          <div
            className={`truncate ${
              isSelected ? "text-white" : "text-gray-600"
            }`}
          >
            {data.price_countersunk}
          </div>
        </div>
      ) : (
        data.label
      )}
    </div>
  );

  // Custom menu component to add header
  const CustomMenu = ({ children, innerProps, className, style }) => (
    <div {...innerProps} className={className} style={style}>
      {showHoleTable && (
        <div className="px-3 py-2 bg-gray-200 border border-gray-600">
          <div className="grid grid-cols-2 gap-4 text-xs font-semibold text-gray-700 uppercase">
            <div>HOLE TYPE</div>
            <div>PRICE</div>
          </div>
        </div>
      )}
      {children}
    </div>
  );

  return (
    <div>
      <Select
        name={name}
        value={selected}
        onChange={handleChange}
        options={options}
        isClearable
        menuPlacement={showHoleTable ? "bottom" : menuPlacement}
        placeholder=""
        className="react-select-container border border-gray-600 rounded-sm"
        classNamePrefix="react-select"
        styles={customStyles}
        menuPortalTarget={showHoleTable ? document.body : null}
        components={
          showHoleTable
            ? {
                Option: CustomOption,
                Menu: CustomMenu,
              }
            : {}
        }
      />
    </div>
  );
}

export default CustomSelect;
