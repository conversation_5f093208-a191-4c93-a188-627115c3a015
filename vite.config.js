import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { VitePWA } from 'vite-plugin-pwa'

export default defineConfig(() => {
  const API_BASE_URL = process.env.VITE_API_BASE_URL || 'http://localhost:3000'

  return {
    plugins: [
      react(),
      VitePWA({
        registerType: 'autoUpdate',
        manifest: {
          name: 'My App',
          short_name: 'MyApp',
          start_url: '/',
          display: 'fullscreen',
          background_color: '#ffffff',
          theme_color: '#000000',
          icons: [
            { src: '/pwa-icon.png', sizes: '192x192', type: 'image/png' },
            { src: '/pwa-icon.png', sizes: '512x512', type: 'image/png' }
          ]
        }
      })
    ],
    server: {
      host: '0.0.0.0',
      port: 19090,
      proxy: {
        '/api': {
          target: API_BASE_URL,
          changeOrigin: true,
          secure: false
        }
      }
    }
  }
})
